<template>
  <div class="room-status-container">
    <h2 class="title">会议室状态显示屏</h2>
    <div class="date">{{ currentDate }}</div>
    <el-row :gutter="20">
      <el-col v-for="room in roomList" :key="room.roomId" :span="12" class="room-card-col">
        <el-card class="room-card">
          <div class="room-header">
            <span class="room-name">{{ room.roomName }} ({{ room.capacity }}人)</span>
            <el-tag :type="room.status === 1 ? 'success' : 'info'">
              {{ formatStatus(room.status) }}
            </el-tag>
          </div>
          <div v-if="room.todayMeetings && room.todayMeetings.length">
            <div v-for="(meeting, idx) in room.todayMeetings" :key="idx" class="meeting-block">
              <div class="meeting-time">{{ meeting.startTime }} - {{ meeting.endTime }}</div>
              <div class="meeting-title">{{ meeting.meetingTitle }}</div>
              <div class="meeting-progress">进度: {{ meeting.progress }}</div>
              <div class="meeting-applicant">申请人: {{ meeting.applicant }}</div>
            </div>
          </div>
          <div v-else class="no-meeting">当前无会议</div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { listRoom } from '@/api/search/room'
import { listReservation } from '@/api/reserve/reservation'

const roomList = ref([])
const currentDate = ref('')

function formatStatus(status) {
  const map = { 0: '停用', 1: '启用', 2: '维护中' }
  return map[status] || '未知'
}

function getCurrentDate() {
  const now = new Date()
  const week = ['日','一','二','三','四','五','六']
  return `${now.getFullYear()}年${now.getMonth()+1}月${now.getDate()}日 星期${week[now.getDay()]} ${now.toLocaleTimeString()}`
}

async function fetchRoomsAndMeetings() {
  // 获取会议室列表
  const roomRes = await listRoom({})
  const rooms = roomRes.rows || []
  // 获取所有会议室的预约列表（已通过）
  const meetingRes = await listReservation({ status: 2 })
  const meetings = meetingRes.rows || []
  // 组装会议室状态，展示当天所有已通过的会议
  const now = new Date()
  const todayStr = `${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')}`
  roomList.value = rooms.map(room => {
    // 查找该会议室当天所有已通过的会议
    const todayMeetings = meetings.filter(m => {
      if (!m.startTime) return false
      // 兼容各种时间格式，取日期部分
      let dateStr = ''
      try {
        const d = new Date(m.startTime.replace(/-/g, '/'))
        dateStr = `${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,'0')}-${String(d.getDate()).padStart(2,'0')}`
      } catch {
        dateStr = m.startTime.slice(0, 10)
      }
      return m.roomId === room.roomId && dateStr === todayStr
    })
    return {
      ...room,
      todayMeetings: todayMeetings.map(meeting => {
        // 计算会议进度
        let progress = '0%'
        try {
          const nowTime = new Date()
          const start = new Date(meeting.startTime.replace(/-/g, '/'))
          const end = new Date(meeting.endTime.replace(/-/g, '/'))
          if (nowTime >= end) {
            progress = '100%'
          } else if (nowTime <= start) {
            progress = '0%'
          } else {
            progress = Math.round(((nowTime - start) / (end - start)) * 100) + '%'
          }
        } catch {}
        return {
          startTime: meeting.startTime,
          endTime: meeting.endTime,
          meetingTitle: meeting.meetingTitle,
          applicant: meeting.applyUserName || meeting.applicantName || meeting.createBy || meeting.userName || '',
          progress
        }
      })
    }
  })
}

onMounted(() => {
  currentDate.value = getCurrentDate()
  fetchRoomsAndMeetings()
  setInterval(() => {
    currentDate.value = getCurrentDate()
  }, 1000)
})
</script>

<style scoped>
.room-status-container {
  background: linear-gradient(135deg, #2b5876 0%, #4e4376 100%);
  min-height: 100vh;
  padding: 30px;
}
.title {
  color: #fff;
  text-align: center;
  font-size: 2rem;
  margin-bottom: 10px;
}
.date {
  color: #fff;
  text-align: center;
  margin-bottom: 30px;
}
.room-card-col {
  margin-bottom: 20px;
}
.room-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  background: #fff;
}
.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.room-name {
  font-weight: bold;
  font-size: 1.2rem;
}
.meeting-time {
  font-size: 1rem;
  color: #333;
}
.meeting-title {
  font-size: 1rem;
  color: #666;
  margin-bottom: 5px;
}
.meeting-progress {
  font-size: 0.95rem;
  color: #888;
}
.meeting-attendees {
  font-size: 0.95rem;
  color: #888;
}
.no-meeting {
  color: #aaa;
  text-align: center;
  padding: 20px 0;
}
</style>
